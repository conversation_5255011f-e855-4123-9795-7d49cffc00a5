# GymKod Redis Production Kurulum Script'i
# Windows Server 2019 için optimize edilmiş
# Çalıştırmadan önce: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

param(
    [string]$InstallPath = "C:\GymProject",
    [string]$RedisPassword = "GymKod2024Redis!Production",
    [switch]$SkipDockerInstall = $false
)

# Renkli çıktı için fonksiyonlar
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

Write-Info "GymKod Redis Production Kurulum Başlatılıyor..."
Write-Info "Kurulum Dizini: $InstallPath"
Write-Info "Redis Şifresi: $RedisPassword"

# Yönetici yetkisi kontrolü
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "Bu script'i Administrator olarak çalıştırmanız gerekiyor!"
    Write-Info "PowerShell'i sağ tıklayıp 'Run as Administrator' seçin"
    exit 1
}

Write-Success "Administrator yetkileri doğrulandı"

# Kurulum dizini oluşturma
Write-Info "Kurulum dizini oluşturuluyor: $InstallPath"
if (!(Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Write-Success "Kurulum dizini oluşturuldu"
} else {
    Write-Info "Kurulum dizini zaten mevcut"
}

# Alt dizinleri oluştur
$subDirs = @("logs", "backups", "config")
foreach ($dir in $subDirs) {
    $fullPath = Join-Path $InstallPath $dir
    if (!(Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Success "$dir dizini oluşturuldu"
    }
}

# Docker kurulum kontrolü
if (-not $SkipDockerInstall) {
    Write-Info "Docker kurulumu kontrol ediliyor..."
    
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-Success "Docker zaten kurulu: $dockerVersion"
        } else {
            throw "Docker bulunamadı"
        }
    } catch {
        Write-Warning "Docker bulunamadı. Chocolatey ile kurulum yapılıyor..."
        
        # Chocolatey kurulum kontrolü
        try {
            choco --version | Out-Null
            Write-Success "Chocolatey zaten kurulu"
        } catch {
            Write-Info "Chocolatey kuruluyor..."
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
            iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
            Write-Success "Chocolatey kuruldu"
        }
        
        # Docker Desktop kurulumu
        Write-Info "Docker Desktop kuruluyor... (Bu işlem 10-15 dakika sürebilir)"
        choco install docker-desktop -y
        Write-Success "Docker Desktop kuruldu. Lütfen sistemi yeniden başlatın ve script'i tekrar çalıştırın."
        exit 0
    }
}

# Docker Compose production dosyası oluşturma
Write-Info "Docker Compose production dosyası oluşturuluyor..."
$dockerComposeContent = @"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.prod.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=$RedisPassword
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "$RedisPassword", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
"@

$dockerComposePath = Join-Path $InstallPath "docker-compose.prod.yml"
$dockerComposeContent | Out-File -FilePath $dockerComposePath -Encoding UTF8
Write-Success "Docker Compose dosyası oluşturuldu: $dockerComposePath"

# Redis production configuration dosyası oluşturma
Write-Info "Redis production configuration dosyası oluşturuluyor..."
$redisConfigContent = @"
# Redis Production Configuration for GymKod
bind 0.0.0.0
port 6379
requirepass $RedisPassword

# Memory management (4GB sunucu için optimize)
maxmemory 1800mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# AOF persistence
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /data/redis.log

# Performance tuning
tcp-keepalive 300
timeout 300

# Security
protected-mode yes

# Database count
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
"@

$redisConfigPath = Join-Path $InstallPath "config\redis.prod.conf"
$redisConfigContent | Out-File -FilePath $redisConfigPath -Encoding UTF8
Write-Success "Redis configuration dosyası oluşturuldu: $redisConfigPath"

# Monitoring script oluşturma
Write-Info "Monitoring script oluşturuluyor..."
$monitoringScript = @"
# Redis Health Check Script
`$redisContainer = "gymkod-redis-prod"
`$logFile = "$InstallPath\logs\redis-monitor.log"

function Write-Log {
    param(`$Message)
    `$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "`$timestamp - `$Message" | Out-File -FilePath `$logFile -Append
}

# Container durumu kontrol
`$containerStatus = docker ps --filter "name=`$redisContainer" --format "{{.Status}}"

if (`$containerStatus -like "*Up*") {
    Write-Log "Redis container çalışıyor: `$containerStatus"
    
    # Redis ping testi
    `$pingResult = docker exec `$redisContainer redis-cli -a "$RedisPassword" ping 2>`$null
    
    if (`$pingResult -eq "PONG") {
        Write-Log "Redis ping başarılı"
    } else {
        Write-Log "Redis ping başarısız - Container yeniden başlatılıyor"
        docker restart `$redisContainer
    }
} else {
    Write-Log "Redis container çalışmıyor - Başlatılıyor"
    Set-Location "$InstallPath"
    docker-compose -f docker-compose.prod.yml up -d
}

# Memory kullanımı kontrol
`$memInfo = docker exec `$redisContainer redis-cli -a "$RedisPassword" info memory | Select-String "used_memory_human"
Write-Log "Memory kullanımı: `$memInfo"
"@

$monitoringScriptPath = Join-Path $InstallPath "monitor-redis.ps1"
$monitoringScript | Out-File -FilePath $monitoringScriptPath -Encoding UTF8
Write-Success "Monitoring script oluşturuldu: $monitoringScriptPath"

# Backup script oluşturma
Write-Info "Backup script oluşturuluyor..."
$backupScript = @"
# Redis Backup Script
`$backupDir = "$InstallPath\backups"
`$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
`$backupFile = "`$backupDir\redis-backup-`$timestamp.rdb"

# Redis BGSAVE komutu
docker exec gymkod-redis-prod redis-cli -a "$RedisPassword" BGSAVE

# RDB dosyasını kopyala
Start-Sleep -Seconds 10
docker cp gymkod-redis-prod:/data/dump.rdb `$backupFile

Write-Host "Backup oluşturuldu: `$backupFile"

# 7 günden eski backup'ları sil
Get-ChildItem `$backupDir -Name "redis-backup-*.rdb" | 
    Where-Object { (Get-Date) - (Get-Item "`$backupDir\`$_").CreationTime -gt (New-TimeSpan -Days 7) } |
    ForEach-Object { Remove-Item "`$backupDir\`$_" -Force }
"@

$backupScriptPath = Join-Path $InstallPath "backup-redis.ps1"
$backupScript | Out-File -FilePath $backupScriptPath -Encoding UTF8
Write-Success "Backup script oluşturuldu: $backupScriptPath"

# Firewall kuralı ekleme
Write-Info "Firewall kuralı ekleniyor..."
try {
    New-NetFirewallRule -DisplayName "Redis Production Port" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -ErrorAction SilentlyContinue
    Write-Success "Firewall kuralı eklendi (Port 6379)"
} catch {
    Write-Warning "Firewall kuralı eklenemedi. Manuel olarak eklemeniz gerekebilir."
}

# Redis container başlatma
Write-Info "Redis container başlatılıyor..."
Set-Location $InstallPath
try {
    docker-compose -f docker-compose.prod.yml up -d
    Start-Sleep -Seconds 10
    
    # Container durumu kontrol
    $containerStatus = docker ps --filter "name=gymkod-redis-prod" --format "{{.Status}}"
    if ($containerStatus -like "*Up*") {
        Write-Success "Redis container başarıyla başlatıldı"
        
        # Ping testi
        $pingResult = docker exec gymkod-redis-prod redis-cli -a $RedisPassword ping 2>$null
        if ($pingResult -eq "PONG") {
            Write-Success "Redis ping testi başarılı: $pingResult"
        } else {
            Write-Warning "Redis ping testi başarısız"
        }
    } else {
        Write-Error "Redis container başlatılamadı"
    }
} catch {
    Write-Error "Redis container başlatılırken hata oluştu: $($_.Exception.Message)"
}

# Scheduled Task oluşturma
Write-Info "Scheduled Task'lar oluşturuluyor..."

# Health Check Task
$healthCheckAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File `"$monitoringScriptPath`""
$healthCheckTrigger = New-ScheduledTaskTrigger -Daily -At "00:00"
$healthCheckSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
Register-ScheduledTask -TaskName "GymKod Redis Health Check" -Action $healthCheckAction -Trigger $healthCheckTrigger -Settings $healthCheckSettings -User "SYSTEM" -Force

# Backup Task
$backupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File `"$backupScriptPath`""
$backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"
$backupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
Register-ScheduledTask -TaskName "GymKod Redis Backup" -Action $backupAction -Trigger $backupTrigger -Settings $backupSettings -User "SYSTEM" -Force

Write-Success "Scheduled Task'lar oluşturuldu"

# Kurulum özeti
Write-Info "`n🎯 KURULUM TAMAMLANDI!"
Write-Info "================================"
Write-Success "✅ Redis container çalışıyor: gymkod-redis-prod"
Write-Success "✅ Configuration dosyaları oluşturuldu"
Write-Success "✅ Monitoring ve backup script'leri hazır"
Write-Success "✅ Scheduled Task'lar oluşturuldu"
Write-Success "✅ Firewall kuralı eklendi"

Write-Info "`n📋 ÖNEMLİ BİLGİLER:"
Write-Info "Redis Şifresi: $RedisPassword"
Write-Info "Redis Port: 6379"
Write-Info "Container Adı: gymkod-redis-prod"
Write-Info "Kurulum Dizini: $InstallPath"

Write-Info "`n🔧 SONRAKI ADIMLAR:"
Write-Info "1. .NET uygulamanızın appsettings.json dosyasını güncelleyin:"
Write-Info "   `"Redis`": { `"canlı`": `"localhost:6379,password=$RedisPassword,abortConnect=false`" }"
Write-Info "2. .NET uygulamanızı yeniden başlatın"
Write-Info "3. Cache endpoint'lerini test edin"

Write-Info "`n🧪 TEST KOMUTLARI:"
Write-Info "docker ps                                    # Container durumu"
Write-Info "docker logs gymkod-redis-prod               # Redis logları"
Write-Info "docker exec -it gymkod-redis-prod redis-cli -a $RedisPassword ping  # Ping testi"

Write-Success "`nKurulum başarıyla tamamlandı! 🎉"
