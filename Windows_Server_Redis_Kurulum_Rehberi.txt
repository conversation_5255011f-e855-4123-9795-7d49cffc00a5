🎯 WINDOWS SERVER 2019 REDİS KURULUM REHBERİ
================================================================
PROJE: GymKod Pro - Production Redis Deployment
SUNUCU: Windows Server 2019 (4GB RAM, 2 CPU)
MEVCUT DURUM: 1 salon, 100 üye
HEDEF: Ölçeklenebilir Redis altyapısı

================================================================
AŞAMA 1: SUNUCU HAZIRLIK (30 dakika)
================================================================

1.1 WINDOWS UPDATE VE GÜVENLİK
-------------------------------
• Windows Server 2019'u güncelleyin:
  - Settings > Update & Security > Windows Update
  - Tüm kritik güncellemeleri yükleyin
  - Sunucuyu yeniden başlatın

• Windows Defender Firewall ayarları:
  - Control Panel > System and Security > Windows Defender Firewall
  - "Advanced settings" tıklayın
  - Inbound Rules > New Rule > Port > TCP > 6379 > Allow
  - Name: "Redis Port 6379"

1.2 GEREKLI YAZILIMLAR
----------------------
• PowerShell 7 kurulumu:
  - https://github.com/PowerShell/PowerShell/releases
  - PowerShell-7.x.x-win-x64.msi indirin ve kurun

• Git kurulumu (opsiyonel):
  - https://git-scm.com/download/win
  - Git-2.x.x-64-bit.exe indirin ve kurun

================================================================
AŞAMA 2: DOCKER KURULUM SEÇENEKLERİ (45 dakika)
================================================================

SEÇenek A: DOCKER DESKTOP (ÖNERİLEN - Kolay yönetim)
----------------------------------------------------
2A.1 Docker Desktop kurulumu:
• https://www.docker.com/products/docker-desktop/ adresine gidin
• "Download for Windows" tıklayın
• Docker Desktop Installer.exe indirin
• Installer'ı çalıştırın:
  - "Use WSL 2 instead of Hyper-V" seçeneğini işaretleyin
  - "Add shortcut to desktop" seçeneğini işaretleyin
• Kurulum tamamlandıktan sonra sunucuyu yeniden başlatın

2A.2 WSL 2 kurulumu (Docker Desktop için gerekli):
• PowerShell'i Administrator olarak açın
• Şu komutları sırayla çalıştırın:
  ```powershell
  dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
  dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
  ```
• Sunucuyu yeniden başlatın
• WSL 2 Linux kernel update package indirin:
  - https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi
• wsl_update_x64.msi'yi çalıştırın
• PowerShell'de: `wsl --set-default-version 2`

2A.3 Docker Desktop'ı başlatın:
• Desktop'tan Docker Desktop'ı çalıştırın
• İlk açılışta Terms of Service'i kabul edin
• "Skip tutorial" seçeneğini seçin
• Sol alt köşede "Engine running" yazısını görene kadar bekleyin

SEÇenek B: DOCKER ENGINE (Sunucu ortamı için)
----------------------------------------------
2B.1 Chocolatey kurulumu:
• PowerShell'i Administrator olarak açın
• Şu komutu çalıştırın:
  ```powershell
  Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
  ```

2B.2 Docker Engine kurulumu:
• PowerShell'de şu komutları çalıştırın:
  ```powershell
  choco install docker-desktop -y
  ```

================================================================
AŞAMA 3: REDİS PRODUCTION KURULUMU (30 dakika)
================================================================

3.1 PRODUCTION DOCKER-COMPOSE DOSYASI OLUŞTURMA
-----------------------------------------------
• Sunucuda C:\GymProject klasörü oluşturun
• C:\GymProject\docker-compose.prod.yml dosyası oluşturun:

```yaml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.prod.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKod2024Redis!Production
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKod2024Redis!Production", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
```

3.2 PRODUCTION REDİS CONFIGURATION
----------------------------------
• C:\GymProject\redis.prod.conf dosyası oluşturun:

```conf
# Redis Production Configuration for GymKod
# Bind to all interfaces
bind 0.0.0.0

# Port
port 6379

# Password protection (STRONG PASSWORD)
requirepass GymKod2024Redis!Production

# Memory management (2GB için optimize)
maxmemory 1800mb
maxmemory-policy allkeys-lru

# Persistence - Production için güçlendirilmiş
save 900 1
save 300 10
save 60 10000

# AOF persistence - Daha güvenli
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /data/redis.log

# Performance tuning
tcp-keepalive 300
timeout 300

# Security
protected-mode yes

# Database count
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
```

================================================================
AŞAMA 4: REDİS CONTAINER BAŞLATMA (15 dakika)
================================================================

4.1 CONTAINER BAŞLATMA
----------------------
• PowerShell'i Administrator olarak açın
• C:\GymProject klasörüne gidin:
  ```powershell
  cd C:\GymProject
  ```

• Redis container'ını başlatın:
  ```powershell
  docker-compose -f docker-compose.prod.yml up -d
  ```

4.2 CONTAINER DURUMU KONTROLÜ
-----------------------------
• Container'ın çalıştığını kontrol edin:
  ```powershell
  docker ps
  ```
  Çıktıda "gymkod-redis-prod" görmelisiniz

• Redis loglarını kontrol edin:
  ```powershell
  docker logs gymkod-redis-prod
  ```

• Redis'e bağlantı testi:
  ```powershell
  docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis!Production ping
  ```
  Çıktı: PONG

================================================================
AŞAMA 5: WINDOWS SERVİS OLARAK ÇALIŞTIRMA (20 dakika)
================================================================

5.1 DOCKER COMPOSE SERVİS OLUŞTURMA
-----------------------------------
• NSSM (Non-Sucking Service Manager) indirin:
  - https://nssm.cc/download
  - nssm-2.24.zip indirin ve C:\nssm klasörüne çıkarın

• PowerShell'i Administrator olarak açın:
  ```powershell
  C:\nssm\win64\nssm.exe install GymKodRedis
  ```

• Açılan pencerede:
  - Path: C:\Program Files\Docker\Docker\resources\bin\docker-compose.exe
  - Startup directory: C:\GymProject
  - Arguments: -f docker-compose.prod.yml up
  - Service name: GymKodRedis

• Servisi başlatın:
  ```powershell
  net start GymKodRedis
  ```

================================================================
AŞAMA 6: MONİTORİNG VE BACKUP (25 dakika)
================================================================

6.1 REDİS MONITORING SCRIPT
---------------------------
• C:\GymProject\monitor-redis.ps1 dosyası oluşturun:

```powershell
# Redis Health Check Script
$redisContainer = "gymkod-redis-prod"
$logFile = "C:\GymProject\logs\redis-monitor.log"

# Log klasörü oluştur
if (!(Test-Path "C:\GymProject\logs")) {
    New-Item -ItemType Directory -Path "C:\GymProject\logs"
}

function Write-Log {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Message" | Out-File -FilePath $logFile -Append
}

# Container durumu kontrol
$containerStatus = docker ps --filter "name=$redisContainer" --format "{{.Status}}"

if ($containerStatus -like "*Up*") {
    Write-Log "Redis container çalışıyor: $containerStatus"
    
    # Redis ping testi
    $pingResult = docker exec $redisContainer redis-cli -a "GymKod2024Redis!Production" ping 2>$null
    
    if ($pingResult -eq "PONG") {
        Write-Log "Redis ping başarılı"
    } else {
        Write-Log "Redis ping başarısız - Container yeniden başlatılıyor"
        docker restart $redisContainer
    }
} else {
    Write-Log "Redis container çalışmıyor - Başlatılıyor"
    docker-compose -f C:\GymProject\docker-compose.prod.yml up -d
}

# Memory kullanımı kontrol
$memInfo = docker exec $redisContainer redis-cli -a "GymKod2024Redis!Production" info memory
Write-Log "Memory bilgisi: $memInfo"
```

6.2 BACKUP SCRIPT
-----------------
• C:\GymProject\backup-redis.ps1 dosyası oluşturun:

```powershell
# Redis Backup Script
$backupDir = "C:\GymProject\backups"
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupFile = "$backupDir\redis-backup-$timestamp.rdb"

# Backup klasörü oluştur
if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir
}

# Redis BGSAVE komutu
docker exec gymkod-redis-prod redis-cli -a "GymKod2024Redis!Production" BGSAVE

# RDB dosyasını kopyala
Start-Sleep -Seconds 10
docker cp gymkod-redis-prod:/data/dump.rdb $backupFile

Write-Host "Backup oluşturuldu: $backupFile"

# 7 günden eski backup'ları sil
Get-ChildItem $backupDir -Name "redis-backup-*.rdb" | 
    Where-Object { (Get-Date) - (Get-Item "$backupDir\$_").CreationTime -gt (New-TimeSpan -Days 7) } |
    ForEach-Object { Remove-Item "$backupDir\$_" }
```

6.3 SCHEDULED TASK OLUŞTURMA
----------------------------
• Task Scheduler'ı açın (taskschd.msc)
• "Create Basic Task" tıklayın:
  - Name: Redis Health Check
  - Trigger: Daily, 00:00
  - Action: Start a program
  - Program: powershell.exe
  - Arguments: -File "C:\GymProject\monitor-redis.ps1"

• İkinci task oluşturun:
  - Name: Redis Backup
  - Trigger: Daily, 02:00
  - Action: Start a program
  - Program: powershell.exe
  - Arguments: -File "C:\GymProject\backup-redis.ps1"

================================================================
AŞAMA 7: .NET UYGULAMANIZI SUNUCUYA BAĞLAMA (10 dakika)
================================================================

7.1 APPSETTINGS.JSON GÜNCELLEMESİ
---------------------------------
• Sunucunuzdaki .NET uygulamasının appsettings.json dosyasında:

```json
"Redis": {
  "canlı": "localhost:6379,password=GymKod2024Redis!Production,abortConnect=false,connectTimeout=30000,syncTimeout=30000"
}
```

7.2 BAĞLANTI TESTİ
------------------
• .NET uygulamanızı başlatın
• Cache endpoint'lerini test edin:
  - GET /api/member/getmemberdetails
  - GET /api/membershipType/getall

================================================================
AŞAMA 8: GÜVENLİK VE OPTİMİZASYON (15 dakika)
================================================================

8.1 FIREWALL KURALLARI
----------------------
• Windows Defender Firewall > Advanced Settings
• Inbound Rules > New Rule:
  - Rule Type: Port
  - Protocol: TCP
  - Specific Local Ports: 6379
  - Action: Allow the connection
  - Profile: Domain, Private, Public
  - Name: Redis Production Port

8.2 PERFORMANCE MONİTORİNG
--------------------------
• Performance Monitor (perfmon.exe) açın
• Data Collector Sets > User Defined > New > Data Collector Set
• Name: Redis Performance
• Create manually seçin
• Performance Counter seçin
• Add Counters:
  - Memory > Available MBytes
  - Processor > % Processor Time
  - Network Interface > Bytes Total/sec

================================================================
SONUÇ VE TEST
================================================================

✅ BAŞARILI KURULUM KONTROL LİSTESİ:
- [ ] Docker Desktop/Engine çalışıyor
- [ ] Redis container ayakta (docker ps)
- [ ] Redis ping testi başarılı
- [ ] Windows Service olarak çalışıyor
- [ ] Monitoring script'leri çalışıyor
- [ ] Backup script'leri çalışıyor
- [ ] .NET uygulaması Redis'e bağlanıyor
- [ ] Cache endpoint'leri çalışıyor

🎯 PERFORMANCE HEDEFLERI:
- Memory kullanımı: <1.8GB (2GB limit)
- Response time: <100ms (cache hit)
- Uptime: %99.9+
- Backup: Günlük otomatik

⚠️ ÖNEMLİ NOTLAR:
1. Production şifresi güçlü tutun
2. Backup'ları düzenli kontrol edin
3. Memory kullanımını izleyin
4. Log dosyalarını düzenli temizleyin
5. Windows Update'leri düzenli yapın

================================================================
SORUN GİDERME
================================================================

SORUN: Docker başlamıyor
ÇÖZÜM: WSL 2 kurulumunu kontrol edin, Hyper-V'yi etkinleştirin

SORUN: Redis container başlamıyor
ÇÖZÜM: Port 6379'un başka bir uygulama tarafından kullanılmadığını kontrol edin

SORUN: .NET uygulaması Redis'e bağlanamıyor
ÇÖZÜM: Connection string'i ve şifreyi kontrol edin

SORUN: Memory kullanımı yüksek
ÇÖZÜM: redis.prod.conf'da maxmemory değerini düşürün

================================================================
HIZLI KURULUM (OTOMATİK SCRIPT)
================================================================

Yukarıdaki adımları otomatik olarak yapmak için:

1. PowerShell'i Administrator olarak açın
2. Şu komutu çalıştırın:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
3. Redis_Kurulum_Script.ps1 dosyasını çalıştırın:
   ```powershell
   .\Redis_Kurulum_Script.ps1
   ```

Script otomatik olarak:
- Docker kurulumunu kontrol eder
- Redis container'ını kurar
- Configuration dosyalarını oluşturur
- Monitoring ve backup script'lerini hazırlar
- Scheduled Task'ları oluşturur
- Firewall kurallarını ekler

================================================================
.NET UYGULAMANIZI SUNUCUYA BAĞLAMA
================================================================

Kurulum tamamlandıktan sonra .NET uygulamanızın appsettings.json dosyasında:

```json
"Redis": {
  "canlı": "localhost:6379,password=GymKod2024Redis!Production,abortConnect=false,connectTimeout=30000,syncTimeout=30000"
}
```

Ardından uygulamanızı yeniden başlatın ve cache endpoint'lerini test edin.

================================================================
İLETİŞİM VE DESTEK
================================================================

Bu rehberi takip ederken sorun yaşarsanız:
1. Hata mesajlarını tam olarak kaydedin
2. Docker logs çıktısını kontrol edin: `docker logs gymkod-redis-prod`
3. Windows Event Viewer'ı inceleyin
4. Performance Monitor'dan resource kullanımını kontrol edin
5. Redis_Kurulum_Script.ps1 çıktısını kontrol edin

Kurulum tamamlandıktan sonra sistem 100 üyeli 1 salon için optimize edilmiş olacak.
Büyüme durumunda sunucu kapasitesini artırabilirsiniz.

🎯 BAŞARILI KURULUM SONRASI BEKLENEN PERFORMANS:
- Cache hit oranı: %90+
- Response time: <100ms (cache hit)
- Memory kullanımı: <1.8GB
- Uptime: %99.9+
